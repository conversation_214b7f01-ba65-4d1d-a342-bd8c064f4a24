"""
Pydantic models for stock data
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum

class TimeInterval(str, Enum):
    """Supported time intervals for stock data"""
    ONE_MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    THIRTY_MINUTES = "30m"
    ONE_HOUR = "1h"
    ONE_DAY = "1d"
    ONE_WEEK = "1wk"
    ONE_MONTH = "1mo"

class TimePeriod(str, Enum):
    """Supported time periods for historical data"""
    ONE_DAY = "1d"
    FIVE_DAYS = "5d"
    ONE_MONTH = "1mo"
    THREE_MONTHS = "3mo"
    SIX_MONTHS = "6mo"
    ONE_YEAR = "1y"
    TWO_YEARS = "2y"
    FIVE_YEARS = "5y"
    TEN_YEARS = "10y"
    MAX = "max"

class MarketStatus(str, Enum):
    """Market status enumeration"""
    OPEN = "open"
    CLOSED = "closed"
    PRE_MARKET = "pre_market"
    POST_MARKET = "post_market"
    HOLIDAY = "holiday"

class StockInfo(BaseModel):
    """Basic stock information"""
    symbol: str = Field(..., description="Stock symbol (e.g., RELIANCE.NS)")
    name: str = Field(..., description="Company name")
    exchange: str = Field(default="NSE", description="Exchange name")
    currency: str = Field(default="INR", description="Currency")
    market_cap: Optional[float] = Field(None, description="Market capitalization")
    sector: Optional[str] = Field(None, description="Business sector")
    industry: Optional[str] = Field(None, description="Industry classification")

class OHLCV(BaseModel):
    """OHLCV (Open, High, Low, Close, Volume) data point"""
    timestamp: datetime = Field(..., description="Data timestamp")
    open: float = Field(..., description="Opening price")
    high: float = Field(..., description="Highest price")
    low: float = Field(..., description="Lowest price")
    close: float = Field(..., description="Closing price")
    volume: int = Field(..., description="Trading volume")
    
    @validator('open', 'high', 'low', 'close')
    def validate_prices(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v
    
    @validator('volume')
    def validate_volume(cls, v):
        if v < 0:
            raise ValueError('Volume cannot be negative')
        return v

class StockData(BaseModel):
    """Complete stock data response"""
    symbol: str = Field(..., description="Stock symbol")
    info: StockInfo = Field(..., description="Stock information")
    data: List[OHLCV] = Field(..., description="OHLCV data points")
    interval: TimeInterval = Field(..., description="Data interval")
    period: TimePeriod = Field(..., description="Data period")
    last_updated: datetime = Field(..., description="Last update timestamp")

class RealTimePrice(BaseModel):
    """Real-time price data"""
    symbol: str = Field(..., description="Stock symbol")
    price: float = Field(..., description="Current price")
    change: float = Field(..., description="Price change")
    change_percent: float = Field(..., description="Percentage change")
    volume: int = Field(..., description="Current volume")
    timestamp: datetime = Field(..., description="Price timestamp")
    market_status: MarketStatus = Field(..., description="Current market status")
    
    @validator('price')
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('Price must be positive')
        return v

class StockSearchResult(BaseModel):
    """Stock search result"""
    symbol: str = Field(..., description="Stock symbol")
    name: str = Field(..., description="Company name")
    exchange: str = Field(..., description="Exchange")
    type: str = Field(default="equity", description="Security type")
    currency: str = Field(default="INR", description="Currency")

class MarketStatusResponse(BaseModel):
    """Market status response"""
    status: MarketStatus = Field(..., description="Current market status")
    is_open: bool = Field(..., description="Whether market is currently open")
    next_open: Optional[datetime] = Field(None, description="Next market open time")
    next_close: Optional[datetime] = Field(None, description="Next market close time")
    timezone: str = Field(default="Asia/Kolkata", description="Market timezone")
    last_updated: datetime = Field(..., description="Status last updated")

class TechnicalIndicator(BaseModel):
    """Technical indicator data"""
    name: str = Field(..., description="Indicator name")
    values: List[float] = Field(..., description="Indicator values")
    timestamps: List[datetime] = Field(..., description="Corresponding timestamps")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Indicator parameters")

class WebSocketMessage(BaseModel):
    """WebSocket message structure"""
    type: str = Field(..., description="Message type")
    symbol: Optional[str] = Field(None, description="Stock symbol")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    code: str = Field(..., description="Error code")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")

# Request models
class StockDataRequest(BaseModel):
    """Request model for stock data"""
    symbol: str = Field(..., description="Stock symbol")
    period: TimePeriod = Field(default=TimePeriod.ONE_DAY, description="Data period")
    interval: TimeInterval = Field(default=TimeInterval.ONE_MINUTE, description="Data interval")

class StockSearchRequest(BaseModel):
    """Request model for stock search"""
    query: str = Field(..., min_length=1, max_length=50, description="Search query")
    limit: int = Field(default=10, ge=1, le=50, description="Maximum results")
    exchange: Optional[str] = Field(default="NSE", description="Exchange filter")

# Response models
class APIResponse(BaseModel):
    """Generic API response wrapper"""
    success: bool = Field(..., description="Request success status")
    data: Optional[Any] = Field(None, description="Response data")
    error: Optional[ErrorResponse] = Field(None, description="Error information")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
