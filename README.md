# Real-Time NSE Stock Trading Chart Application

A modern web application for real-time NSE stock market data visualization with interactive charts and technical analysis.

## Features

- **Real-time Stock Charts**: Interactive candlestick/OHLC charts using TradingView Lightweight Charts
- **NSE Data Integration**: Live and historical data from National Stock Exchange of India
- **WebSocket Streaming**: Real-time price updates without manual refresh
- **Technical Indicators**: Built-in technical analysis tools
- **Responsive Design**: Works on desktop and mobile devices
- **Market Status**: Live market open/closed status indicator

## Technology Stack

### Backend (Python)
- **FastAPI**: High-performance async web framework
- **WebSocket**: Real-time data streaming
- **yfinance**: Primary data source for NSE stocks
- **pandas/numpy**: Financial data processing
- **SQLite**: Caching layer for historical data

### Frontend (React)
- **React + Vite**: Modern frontend framework with fast development
- **TradingView Lightweight Charts**: Professional financial charting
- **Tailwind CSS**: Utility-first CSS framework
- **WebSocket Client**: Real-time data connection

## Project Structure

```
stock-trading-app/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── main.py         # FastAPI application entry point
│   │   ├── models/         # Pydantic data models
│   │   ├── services/       # Business logic and data services
│   │   ├── api/            # API route handlers
│   │   └── websocket/      # WebSocket handlers
│   ├── requirements.txt    # Python dependencies
│   └── .env               # Environment variables
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── services/       # API and WebSocket clients
│   │   ├── hooks/          # Custom React hooks
│   │   └── utils/          # Utility functions
│   ├── package.json       # Node.js dependencies
│   └── vite.config.js     # Vite configuration
└── README.md              # This file
```

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup
1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Create virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the backend server:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup
1. Navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start development server:
   ```bash
   npm run dev
   ```

## API Endpoints

- `GET /api/stocks/search?q={query}` - Search NSE stocks
- `GET /api/stocks/{symbol}/data` - Get historical OHLC data
- `GET /api/market/status` - Get current market status
- `WebSocket /ws/stocks/{symbol}` - Real-time price updates

## Environment Variables

Create a `.env` file in the backend directory:

```env
# API Keys (optional)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key

# Database
DATABASE_URL=sqlite:///./stock_data.db

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

## Usage

1. Start both backend and frontend servers
2. Open your browser to `http://localhost:5173`
3. Search for NSE stocks using the search bar
4. Select a stock to view its real-time chart
5. Use timeframe controls to switch between different intervals
6. Charts update automatically with live market data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
