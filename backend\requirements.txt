# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# WebSocket support
websockets==12.0

# Data processing
pandas==2.1.3
numpy==1.25.2

# Financial data sources
yfinance==0.2.28
alpha-vantage==2.3.1

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# Database
sqlalchemy==2.0.23
aiosqlite==0.19.0

# Environment variables
python-dotenv==1.0.0

# Data validation
pydantic==2.5.0

# CORS support
python-multipart==0.0.6

# Caching
redis==5.0.1
aioredis==2.0.1

# Logging
loguru==0.7.2

# Date/time handling
python-dateutil==2.8.2
pytz==2023.3

# JSON handling
orjson==3.9.10

# Rate limiting
slowapi==0.1.9

# Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
