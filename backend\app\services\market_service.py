"""
Market service for handling NSE market status and timing
"""

import asyncio
from datetime import datetime, time, timedelta
from typing import Optional, Dict, Any
import pytz
import logging
from ..models.stock_models import MarketStatus, MarketStatusResponse

logger = logging.getLogger(__name__)

class MarketService:
    """Service for managing market status and timing"""
    
    def __init__(self):
        self.timezone = pytz.timezone('Asia/Kolkata')
        self.current_status = MarketStatus.CLOSED
        self.last_status_update = datetime.now()
        
        # NSE trading hours (IST)
        self.market_open_time = time(9, 15)  # 9:15 AM
        self.market_close_time = time(15, 30)  # 3:30 PM
        self.pre_market_start = time(9, 0)   # 9:00 AM
        self.post_market_end = time(16, 0)   # 4:00 PM
        
        # Market holidays (simplified - in production, use a comprehensive holiday calendar)
        self.market_holidays = [
            # Add major NSE holidays here
            # Format: (month, day) for fixed holidays
            (1, 26),   # Republic Day
            (8, 15),   # Independence Day
            (10, 2),   # <PERSON>
        ]
    
    async def initialize(self):
        """Initialize market service"""
        logger.info("Initializing Market Service...")
        await self._update_market_status()
        logger.info("Market Service initialized successfully")
    
    async def cleanup(self):
        """Cleanup market service"""
        logger.info("Market Service cleaned up")
    
    def _get_current_ist_time(self) -> datetime:
        """Get current time in IST"""
        return datetime.now(self.timezone)
    
    def _is_market_holiday(self, date: datetime) -> bool:
        """Check if given date is a market holiday"""
        # Check if it's a weekend
        if date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return True
        
        # Check fixed holidays
        for month, day in self.market_holidays:
            if date.month == month and date.day == day:
                return True
        
        return False
    
    def _determine_market_status(self, current_time: datetime) -> MarketStatus:
        """Determine current market status based on time"""
        if self._is_market_holiday(current_time):
            return MarketStatus.HOLIDAY
        
        current_time_only = current_time.time()
        
        if current_time_only < self.pre_market_start:
            return MarketStatus.CLOSED
        elif self.pre_market_start <= current_time_only < self.market_open_time:
            return MarketStatus.PRE_MARKET
        elif self.market_open_time <= current_time_only < self.market_close_time:
            return MarketStatus.OPEN
        elif self.market_close_time <= current_time_only < self.post_market_end:
            return MarketStatus.POST_MARKET
        else:
            return MarketStatus.CLOSED
    
    async def _update_market_status(self):
        """Update the current market status"""
        current_time = self._get_current_ist_time()
        new_status = self._determine_market_status(current_time)
        
        if new_status != self.current_status:
            logger.info(f"Market status changed from {self.current_status} to {new_status}")
            self.current_status = new_status
        
        self.last_status_update = current_time
    
    async def get_market_status(self) -> MarketStatusResponse:
        """Get current market status"""
        await self._update_market_status()
        current_time = self._get_current_ist_time()
        
        # Calculate next open/close times
        next_open = self._get_next_market_open(current_time)
        next_close = self._get_next_market_close(current_time)
        
        return MarketStatusResponse(
            status=self.current_status,
            is_open=(self.current_status == MarketStatus.OPEN),
            next_open=next_open,
            next_close=next_close,
            timezone="Asia/Kolkata",
            last_updated=self.last_status_update
        )
    
    def _get_next_market_open(self, current_time: datetime) -> Optional[datetime]:
        """Get the next market open time"""
        if self.current_status == MarketStatus.OPEN:
            return None
        
        # If market is closed, find next trading day
        next_day = current_time.date()
        
        # If it's after market hours today, start from tomorrow
        if current_time.time() >= self.market_close_time:
            next_day += timedelta(days=1)
        
        # Find next non-holiday weekday
        while True:
            next_datetime = self.timezone.localize(
                datetime.combine(next_day, self.market_open_time)
            )
            
            if not self._is_market_holiday(next_datetime):
                return next_datetime
            
            next_day += timedelta(days=1)
    
    def _get_next_market_close(self, current_time: datetime) -> Optional[datetime]:
        """Get the next market close time"""
        if self.current_status != MarketStatus.OPEN:
            return None
        
        # If market is open, return today's close time
        today_close = self.timezone.localize(
            datetime.combine(current_time.date(), self.market_close_time)
        )
        
        return today_close
    
    async def is_market_open(self) -> bool:
        """Check if market is currently open"""
        await self._update_market_status()
        return self.current_status == MarketStatus.OPEN
    
    async def is_trading_day(self, date: Optional[datetime] = None) -> bool:
        """Check if given date (or today) is a trading day"""
        if date is None:
            date = self._get_current_ist_time()
        
        return not self._is_market_holiday(date)
    
    async def get_trading_hours(self) -> Dict[str, str]:
        """Get market trading hours"""
        return {
            "pre_market_start": self.pre_market_start.strftime("%H:%M"),
            "market_open": self.market_open_time.strftime("%H:%M"),
            "market_close": self.market_close_time.strftime("%H:%M"),
            "post_market_end": self.post_market_end.strftime("%H:%M"),
            "timezone": "Asia/Kolkata"
        }
    
    async def update_market_status_loop(self):
        """Background task to continuously update market status"""
        while True:
            try:
                await self._update_market_status()
                # Update every minute
                await asyncio.sleep(60)
            except Exception as e:
                logger.error(f"Error in market status update loop: {e}")
                await asyncio.sleep(60)  # Continue even on error
    
    def get_market_session_info(self) -> Dict[str, Any]:
        """Get detailed market session information"""
        current_time = self._get_current_ist_time()
        
        return {
            "current_time": current_time.isoformat(),
            "market_status": self.current_status.value,
            "is_trading_day": not self._is_market_holiday(current_time),
            "trading_hours": {
                "pre_market": f"{self.pre_market_start} - {self.market_open_time}",
                "regular": f"{self.market_open_time} - {self.market_close_time}",
                "post_market": f"{self.market_close_time} - {self.post_market_end}"
            },
            "timezone": "Asia/Kolkata"
        }
