"""
WebSocket connection manager for handling real-time stock data connections
"""

import json
import asyncio
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time stock data"""
    
    def __init__(self):
        # Structure: {symbol: {websocket: connection_info}}
        self.active_connections: Dict[str, Dict[WebSocket, dict]] = {}
        self.connection_count = 0
    
    async def connect(self, websocket: WebSocket, symbol: str):
        """Accept a new WebSocket connection for a specific symbol"""
        await websocket.accept()
        
        if symbol not in self.active_connections:
            self.active_connections[symbol] = {}
        
        connection_info = {
            "connected_at": datetime.now(),
            "symbol": symbol,
            "last_ping": datetime.now()
        }
        
        self.active_connections[symbol][websocket] = connection_info
        self.connection_count += 1
        
        logger.info(f"New WebSocket connection for {symbol}. Total connections: {self.connection_count}")
    
    def disconnect(self, websocket: WebSocket, symbol: str):
        """Remove a WebSocket connection"""
        if symbol in self.active_connections:
            if websocket in self.active_connections[symbol]:
                del self.active_connections[symbol][websocket]
                self.connection_count -= 1
                
                # Clean up empty symbol groups
                if not self.active_connections[symbol]:
                    del self.active_connections[symbol]
                
                logger.info(f"WebSocket disconnected for {symbol}. Total connections: {self.connection_count}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific WebSocket connection"""
        try:
            message_str = json.dumps(message, default=str)
            await websocket.send_text(message_str)
        except Exception as e:
            logger.error(f"Error sending personal message: {e}")
    
    async def broadcast_to_symbol(self, message: dict, symbol: str):
        """Broadcast a message to all connections subscribed to a specific symbol"""
        if symbol not in self.active_connections:
            return
        
        message_str = json.dumps(message, default=str)
        disconnected_websockets = []
        
        for websocket in self.active_connections[symbol]:
            try:
                await websocket.send_text(message_str)
            except WebSocketDisconnect:
                disconnected_websockets.append(websocket)
            except Exception as e:
                logger.error(f"Error broadcasting to {symbol}: {e}")
                disconnected_websockets.append(websocket)
        
        # Clean up disconnected websockets
        for websocket in disconnected_websockets:
            self.disconnect(websocket, symbol)
    
    async def broadcast_to_all(self, message: dict):
        """Broadcast a message to all active connections"""
        message_str = json.dumps(message, default=str)
        disconnected_connections = []
        
        for symbol, connections in self.active_connections.items():
            for websocket in connections:
                try:
                    await websocket.send_text(message_str)
                except WebSocketDisconnect:
                    disconnected_connections.append((websocket, symbol))
                except Exception as e:
                    logger.error(f"Error broadcasting to all: {e}")
                    disconnected_connections.append((websocket, symbol))
        
        # Clean up disconnected websockets
        for websocket, symbol in disconnected_connections:
            self.disconnect(websocket, symbol)
    
    def get_connection_count(self) -> int:
        """Get total number of active connections"""
        return self.connection_count
    
    def get_symbol_connections(self, symbol: str) -> int:
        """Get number of connections for a specific symbol"""
        if symbol in self.active_connections:
            return len(self.active_connections[symbol])
        return 0
    
    def get_active_symbols(self) -> List[str]:
        """Get list of symbols with active connections"""
        return list(self.active_connections.keys())
    
    def get_connection_stats(self) -> dict:
        """Get detailed connection statistics"""
        stats = {
            "total_connections": self.connection_count,
            "active_symbols": len(self.active_connections),
            "symbol_breakdown": {}
        }
        
        for symbol, connections in self.active_connections.items():
            stats["symbol_breakdown"][symbol] = {
                "connections": len(connections),
                "oldest_connection": min(
                    conn_info["connected_at"] for conn_info in connections.values()
                ) if connections else None
            }
        
        return stats
    
    async def ping_all_connections(self):
        """Send ping to all connections to check if they're alive"""
        ping_message = {
            "type": "ping",
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_to_all(ping_message)
    
    async def cleanup_stale_connections(self, max_age_minutes: int = 30):
        """Remove connections that haven't responded to pings"""
        current_time = datetime.now()
        stale_connections = []
        
        for symbol, connections in self.active_connections.items():
            for websocket, conn_info in connections.items():
                age_minutes = (current_time - conn_info["last_ping"]).total_seconds() / 60
                if age_minutes > max_age_minutes:
                    stale_connections.append((websocket, symbol))
        
        for websocket, symbol in stale_connections:
            try:
                await websocket.close()
            except:
                pass
            self.disconnect(websocket, symbol)
        
        if stale_connections:
            logger.info(f"Cleaned up {len(stale_connections)} stale connections")
    
    async def handle_client_message(self, websocket: WebSocket, symbol: str, message: str):
        """Handle incoming messages from clients"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "pong":
                # Update last ping time
                if symbol in self.active_connections and websocket in self.active_connections[symbol]:
                    self.active_connections[symbol][websocket]["last_ping"] = datetime.now()
            
            elif message_type == "subscribe":
                # Handle subscription to additional symbols
                new_symbol = data.get("symbol")
                if new_symbol and new_symbol != symbol:
                    await self.connect(websocket, new_symbol)
            
            elif message_type == "unsubscribe":
                # Handle unsubscription
                unsub_symbol = data.get("symbol", symbol)
                self.disconnect(websocket, unsub_symbol)
            
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON message from client: {message}")
        except Exception as e:
            logger.error(f"Error handling client message: {e}")

# Global connection manager instance
connection_manager = ConnectionManager()
