"""
Stock data service for fetching NSE stock data using yfinance and other sources
"""

import asyncio
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import logging
from concurrent.futures import ThreadPoolExecutor
import aiohttp
import json

from ..models.stock_models import (
    StockInfo, OHLCV, StockData, RealTimePrice, StockSearchResult,
    TimeInterval, TimePeriod, MarketStatus
)

logger = logging.getLogger(__name__)

class StockService:
    """Service for fetching and managing stock data"""
    
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.cache = {}  # Simple in-memory cache
        self.cache_ttl = 300  # 5 minutes cache TTL
        self.nse_symbols = {}  # Cache for NSE symbols
        
    async def initialize(self):
        """Initialize the stock service"""
        logger.info("Initializing Stock Service...")
        await self._load_nse_symbols()
        logger.info("Stock Service initialized successfully")
    
    async def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=True)
        logger.info("Stock Service cleaned up")
    
    async def _load_nse_symbols(self):
        """Load NSE symbols list for search functionality"""
        try:
            # This is a simplified list - in production, you'd load from a comprehensive source
            popular_nse_stocks = [
                {"symbol": "RELIANCE.NS", "name": "Reliance Industries Limited"},
                {"symbol": "TCS.NS", "name": "Tata Consultancy Services Limited"},
                {"symbol": "HDFCBANK.NS", "name": "HDFC Bank Limited"},
                {"symbol": "INFY.NS", "name": "Infosys Limited"},
                {"symbol": "HINDUNILVR.NS", "name": "Hindustan Unilever Limited"},
                {"symbol": "ICICIBANK.NS", "name": "ICICI Bank Limited"},
                {"symbol": "KOTAKBANK.NS", "name": "Kotak Mahindra Bank Limited"},
                {"symbol": "BHARTIARTL.NS", "name": "Bharti Airtel Limited"},
                {"symbol": "ITC.NS", "name": "ITC Limited"},
                {"symbol": "SBIN.NS", "name": "State Bank of India"},
                {"symbol": "LT.NS", "name": "Larsen & Toubro Limited"},
                {"symbol": "ASIANPAINT.NS", "name": "Asian Paints Limited"},
                {"symbol": "AXISBANK.NS", "name": "Axis Bank Limited"},
                {"symbol": "MARUTI.NS", "name": "Maruti Suzuki India Limited"},
                {"symbol": "SUNPHARMA.NS", "name": "Sun Pharmaceutical Industries Limited"},
                {"symbol": "TITAN.NS", "name": "Titan Company Limited"},
                {"symbol": "NESTLEIND.NS", "name": "Nestle India Limited"},
                {"symbol": "ULTRACEMCO.NS", "name": "UltraTech Cement Limited"},
                {"symbol": "POWERGRID.NS", "name": "Power Grid Corporation of India Limited"},
                {"symbol": "NTPC.NS", "name": "NTPC Limited"},
            ]
            
            for stock in popular_nse_stocks:
                self.nse_symbols[stock["symbol"]] = stock["name"]
                
            logger.info(f"Loaded {len(self.nse_symbols)} NSE symbols")
            
        except Exception as e:
            logger.error(f"Error loading NSE symbols: {e}")
    
    def _get_cache_key(self, symbol: str, period: str, interval: str) -> str:
        """Generate cache key for stock data"""
        return f"{symbol}_{period}_{interval}"
    
    def _is_cache_valid(self, cache_entry: dict) -> bool:
        """Check if cache entry is still valid"""
        if not cache_entry:
            return False
        
        cache_time = cache_entry.get("timestamp")
        if not cache_time:
            return False
        
        return (datetime.now() - cache_time).total_seconds() < self.cache_ttl
    
    async def search_stocks(self, query: str, limit: int = 10) -> List[StockSearchResult]:
        """Search for stocks by name or symbol"""
        try:
            results = []
            query_lower = query.lower()
            
            for symbol, name in self.nse_symbols.items():
                if (query_lower in symbol.lower() or 
                    query_lower in name.lower()):
                    results.append(StockSearchResult(
                        symbol=symbol,
                        name=name,
                        exchange="NSE",
                        type="equity",
                        currency="INR"
                    ))
                    
                    if len(results) >= limit:
                        break
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching stocks: {e}")
            return []
    
    async def get_stock_info(self, symbol: str) -> Optional[StockInfo]:
        """Get basic stock information"""
        try:
            # Run yfinance in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(
                self.executor, 
                lambda: yf.Ticker(symbol)
            )
            
            info = await loop.run_in_executor(
                self.executor,
                lambda: ticker.info
            )
            
            if not info:
                return None
            
            return StockInfo(
                symbol=symbol,
                name=info.get("longName", info.get("shortName", symbol)),
                exchange=info.get("exchange", "NSE"),
                currency=info.get("currency", "INR"),
                market_cap=info.get("marketCap"),
                sector=info.get("sector"),
                industry=info.get("industry")
            )
            
        except Exception as e:
            logger.error(f"Error getting stock info for {symbol}: {e}")
            return None
    
    async def get_stock_data(
        self, 
        symbol: str, 
        period: str = "1d", 
        interval: str = "1m"
    ) -> Optional[Dict[str, Any]]:
        """Get historical stock data"""
        try:
            # Check cache first
            cache_key = self._get_cache_key(symbol, period, interval)
            if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
                logger.info(f"Returning cached data for {symbol}")
                return self.cache[cache_key]["data"]
            
            # Fetch data from yfinance
            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(
                self.executor,
                lambda: yf.Ticker(symbol)
            )
            
            # Get historical data
            hist_data = await loop.run_in_executor(
                self.executor,
                lambda: ticker.history(period=period, interval=interval)
            )
            
            if hist_data.empty:
                logger.warning(f"No data found for {symbol}")
                return None
            
            # Convert to our format
            ohlcv_data = []
            for timestamp, row in hist_data.iterrows():
                ohlcv_data.append({
                    "timestamp": timestamp.isoformat(),
                    "open": float(row["Open"]),
                    "high": float(row["High"]),
                    "low": float(row["Low"]),
                    "close": float(row["Close"]),
                    "volume": int(row["Volume"])
                })
            
            # Get stock info
            stock_info = await self.get_stock_info(symbol)
            
            result = {
                "symbol": symbol,
                "info": stock_info.dict() if stock_info else {"symbol": symbol, "name": symbol},
                "data": ohlcv_data,
                "interval": interval,
                "period": period,
                "last_updated": datetime.now().isoformat()
            }
            
            # Cache the result
            self.cache[cache_key] = {
                "data": result,
                "timestamp": datetime.now()
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return None
    
    async def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest price data for a symbol"""
        try:
            loop = asyncio.get_event_loop()
            ticker = await loop.run_in_executor(
                self.executor,
                lambda: yf.Ticker(symbol)
            )
            
            # Get latest data (1 day with 1 minute interval to get recent price)
            hist_data = await loop.run_in_executor(
                self.executor,
                lambda: ticker.history(period="1d", interval="1m")
            )
            
            if hist_data.empty:
                return None
            
            # Get the most recent data point
            latest = hist_data.iloc[-1]
            previous = hist_data.iloc[-2] if len(hist_data) > 1 else latest
            
            current_price = float(latest["Close"])
            previous_price = float(previous["Close"])
            change = current_price - previous_price
            change_percent = (change / previous_price) * 100 if previous_price != 0 else 0
            
            return {
                "symbol": symbol,
                "price": current_price,
                "change": change,
                "change_percent": change_percent,
                "volume": int(latest["Volume"]),
                "timestamp": hist_data.index[-1].isoformat(),
                "market_status": "open"  # This would be determined by market service
            }
            
        except Exception as e:
            logger.error(f"Error getting latest price for {symbol}: {e}")
            return None
    
    async def get_intraday_data(self, symbol: str, interval: str = "1m") -> Optional[List[Dict]]:
        """Get intraday data for real-time updates"""
        try:
            # Get today's data with specified interval
            data = await self.get_stock_data(symbol, period="1d", interval=interval)
            if data and data.get("data"):
                return data["data"]
            return None
            
        except Exception as e:
            logger.error(f"Error getting intraday data for {symbol}: {e}")
            return None
