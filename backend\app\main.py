"""
Main FastAPI application for Real-Time NSE Stock Trading Chart
"""

import os
import asyncio
from contextlib import asynccontextmanager
from typing import List

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
import uvicorn

from .api import stocks, market
from .websocket.connection_manager import ConnectionManager
from .services.stock_service import StockService
from .services.market_service import MarketService

# Load environment variables
load_dotenv()

# Global services
stock_service = StockService()
market_service = MarketService()
connection_manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    print("🚀 Starting Stock Trading Chart API...")
    
    # Initialize services
    await stock_service.initialize()
    await market_service.initialize()
    
    # Start background tasks
    asyncio.create_task(market_service.update_market_status_loop())
    
    yield
    
    # Shutdown
    print("🛑 Shutting down Stock Trading Chart API...")
    await stock_service.cleanup()
    await market_service.cleanup()

# Create FastAPI app
app = FastAPI(
    title="NSE Stock Trading Chart API",
    description="Real-time stock market data API for NSE stocks with WebSocket support",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5173").split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(stocks.router, prefix="/api/stocks", tags=["stocks"])
app.include_router(market.router, prefix="/api/market", tags=["market"])

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "NSE Stock Trading Chart API",
        "version": "1.0.0",
        "status": "running",
        "market_status": await market_service.get_market_status()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "market_open": await market_service.is_market_open(),
        "active_connections": len(connection_manager.active_connections)
    }

@app.websocket("/ws/stocks/{symbol}")
async def websocket_stock_data(websocket: WebSocket, symbol: str):
    """WebSocket endpoint for real-time stock data"""
    await connection_manager.connect(websocket, symbol)
    
    try:
        # Send initial stock data
        initial_data = await stock_service.get_stock_data(symbol, period="1d", interval="1m")
        if initial_data:
            await connection_manager.send_personal_message(
                {
                    "type": "initial_data",
                    "symbol": symbol,
                    "data": initial_data
                },
                websocket
            )
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for client messages (ping/pong, subscription changes, etc.)
                data = await websocket.receive_text()
                
                # Handle client messages if needed
                if data == "ping":
                    await websocket.send_text("pong")
                    
            except WebSocketDisconnect:
                break
            except Exception as e:
                print(f"WebSocket error for {symbol}: {e}")
                break
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket connection error for {symbol}: {e}")
    finally:
        connection_manager.disconnect(websocket, symbol)

@app.websocket("/ws/market")
async def websocket_market_data(websocket: WebSocket):
    """WebSocket endpoint for general market data"""
    await connection_manager.connect(websocket, "market")
    
    try:
        # Send initial market status
        market_status = await market_service.get_market_status()
        await connection_manager.send_personal_message(
            {
                "type": "market_status",
                "data": market_status
            },
            websocket
        )
        
        # Keep connection alive
        while True:
            try:
                data = await websocket.receive_text()
                if data == "ping":
                    await websocket.send_text("pong")
            except WebSocketDisconnect:
                break
            except Exception as e:
                print(f"Market WebSocket error: {e}")
                break
                
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"Market WebSocket connection error: {e}")
    finally:
        connection_manager.disconnect(websocket, "market")

# Background task to broadcast real-time data
async def broadcast_stock_updates():
    """Background task to broadcast stock updates to connected clients"""
    while True:
        try:
            if connection_manager.active_connections:
                # Get all subscribed symbols
                symbols = set()
                for symbol_connections in connection_manager.active_connections.values():
                    symbols.update(symbol_connections.keys())
                
                # Fetch latest data for all symbols
                for symbol in symbols:
                    if symbol != "market":  # Skip market connections
                        try:
                            latest_data = await stock_service.get_latest_price(symbol)
                            if latest_data:
                                await connection_manager.broadcast_to_symbol(
                                    {
                                        "type": "price_update",
                                        "symbol": symbol,
                                        "data": latest_data
                                    },
                                    symbol
                                )
                        except Exception as e:
                            print(f"Error broadcasting data for {symbol}: {e}")
            
            # Wait before next update (adjust frequency as needed)
            await asyncio.sleep(5)  # Update every 5 seconds
            
        except Exception as e:
            print(f"Error in broadcast loop: {e}")
            await asyncio.sleep(10)  # Wait longer on error

# Start background task when app starts
@app.on_event("startup")
async def startup_event():
    """Start background tasks"""
    asyncio.create_task(broadcast_stock_updates())

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
