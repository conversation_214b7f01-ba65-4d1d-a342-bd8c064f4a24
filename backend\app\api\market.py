"""
Market API endpoints
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging

from ..models.stock_models import MarketStatusResponse, APIResponse
from ..services.market_service import MarketService

logger = logging.getLogger(__name__)
router = APIRouter()

# Dependency to get market service
async def get_market_service() -> MarketService:
    service = MarketService()
    await service.initialize()
    return service

@router.get("/status", response_model=MarketStatusResponse)
async def get_market_status(
    market_service: MarketService = Depends(get_market_service)
):
    """Get current NSE market status"""
    try:
        status = await market_service.get_market_status()
        return status
    except Exception as e:
        logger.error(f"Error getting market status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/hours")
async def get_trading_hours(
    market_service: MarketService = Depends(get_market_service)
):
    """Get NSE trading hours"""
    try:
        hours = await market_service.get_trading_hours()
        return APIResponse(
            success=True,
            data=hours,
            error=None
        )
    except Exception as e:
        logger.error(f"Error getting trading hours: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/session")
async def get_market_session(
    market_service: MarketService = Depends(get_market_service)
):
    """Get detailed market session information"""
    try:
        session_info = market_service.get_market_session_info()
        return APIResponse(
            success=True,
            data=session_info,
            error=None
        )
    except Exception as e:
        logger.error(f"Error getting market session info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/is-open")
async def is_market_open(
    market_service: MarketService = Depends(get_market_service)
):
    """Check if market is currently open"""
    try:
        is_open = await market_service.is_market_open()
        return APIResponse(
            success=True,
            data={"is_open": is_open},
            error=None
        )
    except Exception as e:
        logger.error(f"Error checking if market is open: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/is-trading-day")
async def is_trading_day(
    market_service: MarketService = Depends(get_market_service)
):
    """Check if today is a trading day"""
    try:
        is_trading = await market_service.is_trading_day()
        return APIResponse(
            success=True,
            data={"is_trading_day": is_trading},
            error=None
        )
    except Exception as e:
        logger.error(f"Error checking if today is trading day: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
