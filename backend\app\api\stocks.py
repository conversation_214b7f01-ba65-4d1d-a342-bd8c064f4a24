"""
Stock API endpoints
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
import logging

from ..models.stock_models import (
    StockData, StockSearchResult, RealTimePrice, StockInfo,
    APIResponse, ErrorResponse, TimeInterval, TimePeriod
)
from ..services.stock_service import StockService

logger = logging.getLogger(__name__)
router = APIRouter()

# Dependency to get stock service
async def get_stock_service() -> StockService:
    # In a real application, this would be injected properly
    # For now, we'll create a new instance
    service = StockService()
    await service.initialize()
    return service

@router.get("/search", response_model=List[StockSearchResult])
async def search_stocks(
    q: str = Query(..., min_length=1, max_length=50, description="Search query"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of results"),
    stock_service: StockService = Depends(get_stock_service)
):
    """Search for NSE stocks by name or symbol"""
    try:
        results = await stock_service.search_stocks(q, limit)
        return results
    except Exception as e:
        logger.error(f"Error searching stocks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{symbol}/info", response_model=StockInfo)
async def get_stock_info(
    symbol: str,
    stock_service: StockService = Depends(get_stock_service)
):
    """Get basic information about a stock"""
    try:
        info = await stock_service.get_stock_info(symbol)
        if not info:
            raise HTTPException(status_code=404, detail=f"Stock {symbol} not found")
        return info
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock info for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{symbol}/data")
async def get_stock_data(
    symbol: str,
    period: TimePeriod = Query(TimePeriod.ONE_DAY, description="Data period"),
    interval: TimeInterval = Query(TimeInterval.ONE_MINUTE, description="Data interval"),
    stock_service: StockService = Depends(get_stock_service)
):
    """Get historical OHLCV data for a stock"""
    try:
        data = await stock_service.get_stock_data(
            symbol=symbol,
            period=period.value,
            interval=interval.value
        )
        
        if not data:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        return APIResponse(
            success=True,
            data=data,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting stock data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{symbol}/price")
async def get_latest_price(
    symbol: str,
    stock_service: StockService = Depends(get_stock_service)
):
    """Get latest price data for a stock"""
    try:
        price_data = await stock_service.get_latest_price(symbol)
        
        if not price_data:
            raise HTTPException(status_code=404, detail=f"No price data found for {symbol}")
        
        return APIResponse(
            success=True,
            data=price_data,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting latest price for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{symbol}/intraday")
async def get_intraday_data(
    symbol: str,
    interval: TimeInterval = Query(TimeInterval.ONE_MINUTE, description="Data interval"),
    stock_service: StockService = Depends(get_stock_service)
):
    """Get intraday data for a stock"""
    try:
        data = await stock_service.get_intraday_data(
            symbol=symbol,
            interval=interval.value
        )
        
        if not data:
            raise HTTPException(status_code=404, detail=f"No intraday data found for {symbol}")
        
        return APIResponse(
            success=True,
            data=data,
            error=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting intraday data for {symbol}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/popular")
async def get_popular_stocks(
    limit: int = Query(20, ge=1, le=50, description="Number of popular stocks to return"),
    stock_service: StockService = Depends(get_stock_service)
):
    """Get list of popular NSE stocks"""
    try:
        # Return popular stocks from our predefined list
        popular_symbols = [
            "RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "INFY.NS", "HINDUNILVR.NS",
            "ICICIBANK.NS", "KOTAKBANK.NS", "BHARTIARTL.NS", "ITC.NS", "SBIN.NS",
            "LT.NS", "ASIANPAINT.NS", "AXISBANK.NS", "MARUTI.NS", "SUNPHARMA.NS",
            "TITAN.NS", "NESTLEIND.NS", "ULTRACEMCO.NS", "POWERGRID.NS", "NTPC.NS"
        ]
        
        results = []
        for symbol in popular_symbols[:limit]:
            if symbol in stock_service.nse_symbols:
                results.append(StockSearchResult(
                    symbol=symbol,
                    name=stock_service.nse_symbols[symbol],
                    exchange="NSE",
                    type="equity",
                    currency="INR"
                ))
        
        return APIResponse(
            success=True,
            data=results,
            error=None
        )
        
    except Exception as e:
        logger.error(f"Error getting popular stocks: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/indices")
async def get_market_indices():
    """Get major NSE indices"""
    try:
        indices = [
            {"symbol": "^NSEI", "name": "NIFTY 50", "type": "index"},
            {"symbol": "^NSEBANK", "name": "NIFTY BANK", "type": "index"},
            {"symbol": "^NSEIT", "name": "NIFTY IT", "type": "index"},
            {"symbol": "^NSMIDCP", "name": "NIFTY MIDCAP 50", "type": "index"},
            {"symbol": "^NSSMLCP", "name": "NIFTY SMALLCAP 50", "type": "index"},
        ]
        
        return APIResponse(
            success=True,
            data=indices,
            error=None
        )
        
    except Exception as e:
        logger.error(f"Error getting market indices: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
